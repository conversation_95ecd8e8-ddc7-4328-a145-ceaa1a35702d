import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { signal } from '@angular/core';
import { of } from 'rxjs';

import { WireframeTypewriterComponent } from './wireframe-typewriter.component';
import { WireframeMockDataService } from '../../../../services/wireframe-mock-data.service';
import { WireframeGenerationStateService } from '../../../../services/wireframe-generation-state.service';
import { ThemeService } from '../../../../services/theme-service/theme.service';

describe('WireframeTypewriterComponent', () => {
  let component: WireframeTypewriterComponent;
  let fixture: ComponentFixture<WireframeTypewriterComponent>;
  let mockWireframeMockDataService: jasmine.SpyObj<WireframeMockDataService>;
  let mockWireframeGenerationStateService: jasmine.SpyObj<WireframeGenerationStateService>;
  let mockThemeService: jasmine.SpyObj<ThemeService>;

  beforeEach(async () => {
    const wireframeMockDataServiceSpy = jasmine.createSpyObj('WireframeMockDataService', [], {
      currentStep$: of(null)
    });

    const wireframeGenerationStateServiceSpy = jasmine.createSpyObj('WireframeGenerationStateService', [], {
      isGenerating$: of(true)
    });

    const themeServiceSpy = jasmine.createSpyObj('ThemeService', ['getCurrentTheme'], {
      themeObservable: of('light')
    });
    themeServiceSpy.getCurrentTheme.and.returnValue('light');

    await TestBed.configureTestingModule({
      imports: [WireframeTypewriterComponent],
      providers: [
        { provide: WireframeMockDataService, useValue: wireframeMockDataServiceSpy },
        { provide: WireframeGenerationStateService, useValue: wireframeGenerationStateServiceSpy },
        { provide: ThemeService, useValue: themeServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(WireframeTypewriterComponent);
    component = fixture.componentInstance;
    mockWireframeMockDataService = TestBed.inject(WireframeMockDataService) as jasmine.SpyObj<WireframeMockDataService>;
    mockWireframeGenerationStateService = TestBed.inject(WireframeGenerationStateService) as jasmine.SpyObj<WireframeGenerationStateService>;
    mockThemeService = TestBed.inject(ThemeService) as jasmine.SpyObj<ThemeService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with first message', () => {
    fixture.detectChanges();
    expect(component.currentMessage()).toBe('Analyzing wireframe structure and layout patterns...');
    expect(component.currentMessageIndex()).toBe(0);
  });

  it('should cycle through messages with smooth transitions', fakeAsync(() => {
    fixture.detectChanges();
    
    // Initial state
    expect(component.currentMessage()).toBe('Analyzing wireframe structure and layout patterns...');
    expect(component.currentMessageIndex()).toBe(0);
    expect(component.isTransitioning()).toBe(false);

    // Advance time to trigger message rotation
    tick(3500); // MESSAGE_ROTATION_INTERVAL

    // Should start transitioning
    expect(component.isTransitioning()).toBe(true);

    // Advance time for transition duration
    tick(300); // TRANSITION_DURATION

    // Should have moved to next message and stopped transitioning
    expect(component.currentMessage()).toBe('Processing UI components and design elements...');
    expect(component.currentMessageIndex()).toBe(1);
    expect(component.isTransitioning()).toBe(false);

    // Test cycling through more messages
    tick(3500);
    tick(300);
    expect(component.currentMessage()).toBe('Generating responsive layout configurations...');
    expect(component.currentMessageIndex()).toBe(2);
  }));

  it('should loop back to first message after reaching the end', fakeAsync(() => {
    fixture.detectChanges();
    
    // Cycle through all 16 messages
    for (let i = 0; i < 16; i++) {
      tick(3500);
      tick(300);
    }

    // Should be back to the first message
    expect(component.currentMessage()).toBe('Analyzing wireframe structure and layout patterns...');
    expect(component.currentMessageIndex()).toBe(0);
  }));

  it('should reset to first message when generation starts', fakeAsync(() => {
    fixture.detectChanges();
    
    // Advance to a different message
    tick(3500);
    tick(300);
    expect(component.currentMessageIndex()).toBe(1);

    // Simulate generation starting again
    mockWireframeGenerationStateService.isGenerating$ = of(true);
    component.ngOnInit(); // Re-initialize to trigger subscription
    fixture.detectChanges();

    expect(component.currentMessage()).toBe('Analyzing wireframe structure and layout patterns...');
    expect(component.currentMessageIndex()).toBe(0);
    expect(component.isTransitioning()).toBe(false);
  }));

  it('should not transition when not active', fakeAsync(() => {
    // Set generation as not active
    mockWireframeGenerationStateService.isGenerating$ = of(false);
    fixture.detectChanges();
    component.ngOnInit();

    const initialMessage = component.currentMessage();
    const initialIndex = component.currentMessageIndex();

    // Try to advance time
    tick(3500);
    tick(300);

    // Should remain the same
    expect(component.currentMessage()).toBe(initialMessage);
    expect(component.currentMessageIndex()).toBe(initialIndex);
  }));

  it('should apply dark theme class correctly', () => {
    mockThemeService.getCurrentTheme.and.returnValue('dark');
    mockThemeService.themeObservable = of('dark');
    
    component.ngOnInit();
    fixture.detectChanges();

    expect(component.isDarkTheme()).toBe(true);
  });
});
