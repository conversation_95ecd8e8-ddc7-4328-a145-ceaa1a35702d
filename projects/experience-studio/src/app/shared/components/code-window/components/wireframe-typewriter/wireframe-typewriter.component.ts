import {
  Component,
  OnInit,
  OnDestroy,
  ChangeDetectionStrategy,
  inject,
  signal,
  computed
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { interval, map, startWith, switchMap, of, EMPTY } from 'rxjs';
import { WireframeMockDataService } from '../../../../services/wireframe-mock-data.service';
import { WireframeGenerationStateService } from '../../../../services/wireframe-generation-state.service';
import { WireframeGenerationStep } from '../../../../interfaces/wireframe-mock-data.interface';
import { ThemeService } from '../../../../services/theme-service/theme.service';

interface TypewriterState {
  currentText: string;
  isComplete: boolean;
  currentStep: WireframeGenerationStep | null;
}

interface ScrollingTextState {
  currentMessageIndex: number;
  isActive: boolean;
}

@Component({
  selector: 'app-wireframe-typewriter',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="wireframe-loading-display"
         [class.dark-theme]="isDarkTheme()"
         role="status"
         [attr.aria-label]="currentMessage() + ' - Wireframe generation in progress'">

      <!-- Main Content Container -->
      <div class="content-container">

        <!-- Primary Message Display -->
        <div class="primary-message-container">
          <!-- <div class="message-icon" aria-hidden="true">
            <span class="icon-animation">🔧</span>
          </div> -->
          <div class="primary-message">
            {{ currentMessage() }}
          </div>
        </div>

        <!-- Secondary Information Display -->
        <div class="secondary-info-container" *ngIf="currentStepDescription()">
          <div class="secondary-message">
            {{ currentStepDescription() }}
          </div>
        </div>

        <!-- Progress Indicator -->
        <!-- <div class="progress-indicator">
          <div class="progress-dots">
            <span class="dot" [class.active]="currentMessageIndex() >= 0"></span>
            <span class="dot" [class.active]="currentMessageIndex() >= 1"></span>
            <span class="dot" [class.active]="currentMessageIndex() >= 2"></span>
          </div>
        </div> -->

      </div>
    </div>
  `,
  styles: [`
    .wireframe-loading-display {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-top: 24px;
      font-family: 'Mulish', -apple-system, BlinkMacSystemFont, sans-serif;
      opacity: 0;
      animation: fadeIn 0.8s ease-in-out forwards;
      text-align: center;
      min-height: 200px;
    }

    .content-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 20px;
      max-width: 600px;
      width: 100%;
      padding: 0 20px;
    }

    /* Primary Message Styles */
    .primary-message-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
      width: 100%;
    }

    .message-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(135deg, var(--primary-color, #8c65f7) 0%, var(--secondary-color, #e84393) 100%);
      box-shadow: 0 4px 20px rgba(140, 101, 247, 0.3);
    }

    .icon-animation {
      font-size: 24px;
      animation: pulse 2s ease-in-out infinite;
    }

    .primary-message {
      font-size: 18px;
      font-weight: 600;
      color: var(--code-viewer-text, #374151);
      letter-spacing: 0.025em;
      line-height: 1.5;
      text-align: center;
      max-width: 500px;
      transition: all 0.3s ease;
      animation: textFadeIn 0.5s ease-out;
    }

    /* Secondary Information Styles */
    .secondary-info-container {
      width: 100%;
      max-width: 450px;
    }

    .secondary-message {
      font-size: 14px;
      font-weight: 400;
      color: var(--code-viewer-text, #6b7280);
      text-align: center;
      line-height: 1.4;
      opacity: 0.8;
      transition: color 0.3s ease, opacity 0.3s ease;
      animation: textSlideIn 0.6s ease-out;
    }

    /* Progress Indicator Styles */
    .progress-indicator {
      display: flex;
      justify-content: center;
      width: 100%;
      margin-top: 8px;
    }

    .progress-dots {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    .dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: var(--code-viewer-text, #d1d5db);
      opacity: 0.3;
      transition: all 0.3s ease;
    }

    .dot.active {
      background: var(--primary-color, #8c65f7);
      opacity: 1;
      transform: scale(1.2);
    }

    /* Dark theme styles */
    .wireframe-loading-display.dark-theme .primary-message {
      color: var(--code-viewer-text, #e5e7eb);
    }

    .wireframe-loading-display.dark-theme .secondary-message {
      color: var(--code-viewer-text, #9ca3af);
    }

    .wireframe-loading-display.dark-theme .message-icon {
      box-shadow: 0 4px 20px rgba(140, 101, 247, 0.4);
    }

    .wireframe-loading-display.dark-theme .dot {
      background: var(--code-viewer-text, #6b7280);
    }

    .wireframe-loading-display.dark-theme .dot.active {
      background: var(--primary-color, #8c65f7);
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes textFadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes textSlideIn {
      from {
        opacity: 0;
        transform: translateY(15px);
      }
      to {
        opacity: 0.8;
        transform: translateY(0);
      }
    }

    @keyframes pulse {
      0%, 100% {
        transform: scale(1);
        opacity: 0.8;
      }
      50% {
        transform: scale(1.1);
        opacity: 1;
      }
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .wireframe-loading-display {
        margin-top: 16px;
        min-height: 180px;
      }

      .content-container {
        gap: 16px;
        padding: 0 16px;
      }

      .message-icon {
        width: 50px;
        height: 50px;
      }

      .icon-animation {
        font-size: 20px;
      }

      .primary-message {
        font-size: 16px;
        max-width: 400px;
      }

      .secondary-message {
        font-size: 13px;
        max-width: 350px;
      }
    }

    @media (max-width: 480px) {
      .wireframe-loading-display {
        margin-top: 12px;
        min-height: 160px;
      }

      .content-container {
        gap: 12px;
        padding: 0 12px;
      }

      .message-icon {
        width: 45px;
        height: 45px;
      }

      .icon-animation {
        font-size: 18px;
      }

      .primary-message {
        font-size: 15px;
        max-width: 320px;
      }

      .secondary-message {
        font-size: 12px;
        max-width: 280px;
      }

      .dot {
        width: 6px;
        height: 6px;
      }
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class WireframeTypewriterComponent implements OnInit, OnDestroy {
  private readonly wireframeMockDataService = inject(WireframeMockDataService);
  private readonly wireframeGenerationStateService = inject(WireframeGenerationStateService);
  private readonly themeService = inject(ThemeService);

  // Enhanced wireframe-related messages with detailed information
  private readonly wireframeMessages = [
    'Analyzing wireframe structure and layout patterns...',
    'Processing UI components and design elements...',
    'Generating responsive layout configurations...',
    'Optimizing component hierarchy and relationships...',
    'Creating interactive element specifications...',
    'Finalizing wireframe design and structure...',
    'Establishing component architecture and data flow...',
    'Applying design system tokens and variables...',
    'Building accessible navigation structures...',
    'Implementing responsive breakpoint strategies...',
    'Configuring state management patterns...',
    'Optimizing performance and loading strategies...',
    'Generating documentation and style guides...',
    'Validating cross-browser compatibility...',
    'Testing user interaction workflows...',
    'Preparing deployment configurations...'
  ];

  // Signals for reactive state management
  private readonly typewriterState = signal<TypewriterState>({
    currentText: '',
    isComplete: false,
    currentStep: null
  });

  private readonly scrollingState = signal<ScrollingTextState>({
    currentMessageIndex: 0,
    isActive: true
  });

  // Theme signal
  private readonly currentTheme = signal<'light' | 'dark'>(this.themeService.getCurrentTheme());

  // Computed signals for typewriter
  readonly displayText = computed(() => this.typewriterState().currentText);
  readonly isTyping = computed(() => !this.typewriterState().isComplete);
  readonly currentStep = computed(() => this.typewriterState().currentStep);
  readonly currentStepDescription = computed(() => {
    const step = this.currentStep();
    return step?.description || '';
  });

  // Computed signals for scrolling
  readonly currentMessage = computed(() => {
    const state = this.scrollingState();
    return this.wireframeMessages[state.currentMessageIndex] || this.wireframeMessages[0];
  });

  readonly currentMessageIndex = computed(() => {
    const state = this.scrollingState();
    return state.currentMessageIndex;
  });

  readonly isDarkTheme = computed(() => this.currentTheme() === 'dark');

  // Animation configuration
  private readonly TYPING_SPEED = 50; // milliseconds per character
  private readonly MESSAGE_ROTATION_INTERVAL = 2500; // milliseconds - faster rotation for better engagement

  ngOnInit(): void {
    this.initializeTypewriterAnimation();
    this.initializeScrollingAnimation();
    this.initializeThemeSubscription();
  }

  ngOnDestroy(): void {
    // Cleanup handled by takeUntilDestroyed
  }

  private initializeThemeSubscription(): void {
    // Subscribe to theme changes
    this.themeService.themeObservable
      .pipe(takeUntilDestroyed())
      .subscribe(theme => {
        this.currentTheme.set(theme);
      });
  }

  private initializeTypewriterAnimation(): void {
    // Subscribe to current step changes from wireframe mock data service
    this.wireframeMockDataService.currentStep$
      .pipe(
        takeUntilDestroyed(),
        switchMap(step => {
          if (!step) return EMPTY;

          // Update current step
          this.typewriterState.update(state => ({
            ...state,
            currentStep: step,
            isComplete: false
          }));

          // Start typewriter animation for the step title
          return this.createTypewriterAnimation(step.title);
        })
      )
      .subscribe(text => {
        this.typewriterState.update(state => ({
          ...state,
          currentText: text,
          isComplete: text === state.currentStep?.title
        }));
      });

    // Handle generation completion
    this.wireframeGenerationStateService.isGenerating$
      .pipe(takeUntilDestroyed())
      .subscribe(isGenerating => {
        if (!isGenerating) {
          // Reset typewriter state when generation stops
          this.typewriterState.update(state => ({
            ...state,
            currentText: '',
            isComplete: true,
            currentStep: null
          }));
        }
      });
  }

  private initializeScrollingAnimation(): void {
    // Start message rotation interval
    interval(this.MESSAGE_ROTATION_INTERVAL)
      .pipe(takeUntilDestroyed())
      .subscribe(() => {
        this.scrollingState.update(state => ({
          ...state,
          currentMessageIndex: (state.currentMessageIndex + 1) % this.wireframeMessages.length
        }));
      });

    // Handle generation completion
    this.wireframeGenerationStateService.isGenerating$
      .pipe(takeUntilDestroyed())
      .subscribe(isGenerating => {
        this.scrollingState.update(state => ({
          ...state,
          isActive: isGenerating
        }));
      });
  }

  private createTypewriterAnimation(targetText: string) {
    if (!targetText || targetText.length === 0) {
      return of('');
    }

    const chars = targetText.split('');

    return interval(this.TYPING_SPEED).pipe(
      startWith(0),
      map(index => {
        if (index >= chars.length) {
          return targetText;
        }
        return chars.slice(0, index + 1).join('');
      }),
      takeUntilDestroyed()
    );
  }
}
